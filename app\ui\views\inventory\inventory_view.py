from PyQt6.QtWidgets import (
    QW<PERSON>t, QVBoxLayout, QHBoxLayout, QTableView,
    QPushButton, QLineEdit, QComboBox, QLabel,
    QStackedWidget, QFrame, QMenu, QMessageBox, QHeaderView, QSizePolicy
)
from PyQt6.QtCore import Qt, QTimer, QPoint
from PyQt6.QtGui import QIcon, QAction
import asyncio

from .inventory_table_model import InventoryTableModel
from .dialogs.item_dialog import ItemDialog
from .dialogs.movement_dialog import MovementDialog
from .widgets.stock_alert_widget import StockAlertWidget
from .widgets.valuation_widget import ValuationWidget
from .widgets.status_badge_widget import StatusBadgeWidget, AlertBadgeWidget, StatusIndicatorWidget, StatusBadgeDelegate
from .dialogs.price_history_dialog import PriceHistoryDialog
from ...components.custom_widgets import <PERSON>Bar, FilterComboBox, LoadingOverlay
from ...components.custom_filter_proxy_model import CustomFilterProxyModel
from app.utils.event_manager import event_manager, EventType

class InventoryView(QWidget):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_connections()

        # S'abonner aux événements
        self._subscribe_to_events()

        # Enregistrer la référence à cette vue dans le module inventory_updater
        try:
            from app.utils.inventory_updater import set_inventory_view
            import logging
            logger = logging.getLogger(__name__)
            logger.info("Enregistrement de la vue d'inventaire dans le module inventory_updater")
            set_inventory_view(self)
            logger.info("Vue d'inventaire enregistrée avec succès")
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Erreur lors de l'enregistrement de la vue d'inventaire: {e}")
            import traceback
            traceback.print_exc()

        # Use QTimer to properly schedule the async load
        QTimer.singleShot(0, self._init_data)
        self._permissions_applied = False

    def apply_permissions(self, auth_controller):
        """Active/désactive le bouton Nouvel Article selon les permissions utilisateur."""
        if not hasattr(auth_controller, 'has_permission'):
            return
        self.add_button.setEnabled(auth_controller.has_permission('inventory.create'))
        self._permissions_applied = True

    def _subscribe_to_events(self):
        """S'abonne aux événements pertinents pour la vue d'inventaire"""
        # S'abonner à l'événement de mise à jour du prix d'un article
        event_manager.subscribe(EventType.INVENTORY_PRICE_UPDATED, self._handle_price_updated)

        # S'abonner à l'événement de réception d'un article
        event_manager.subscribe(EventType.PURCHASE_ITEM_RECEIVED, self._handle_item_received)

    def _handle_price_updated(self, **kwargs):
        """Gère l'événement de mise à jour du prix d'un article"""
        item_id = kwargs.get('item_id')
        old_purchase_price = kwargs.get('old_purchase_price')
        new_purchase_price = kwargs.get('new_purchase_price')
        old_unit_price = kwargs.get('old_unit_price')
        new_unit_price = kwargs.get('new_unit_price')

        # Afficher un message de confirmation
        print(f"Vue d'inventaire: Prix mis à jour pour l'article {item_id}:")
        print(f"  - Prix d'achat: {old_purchase_price} -> {new_purchase_price}")
        print(f"  - Prix de vente: {old_unit_price} -> {new_unit_price}")

        # Rafraîchir les données de l'inventaire
        self._load_data_wrapper()

    def _handle_item_received(self, **kwargs):
        """Gère l'événement de réception d'un article"""
        item_id = kwargs.get('item_id')
        quantity = kwargs.get('quantity')

        # Afficher un message de confirmation
        print(f"Vue d'inventaire: Article {item_id} reçu, quantité: {quantity}")

        # Rafraîchir les données de l'inventaire
        self._load_data_wrapper()

    def _unsubscribe_from_events(self):
        """Se désabonne des événements"""
        event_manager.unsubscribe(EventType.INVENTORY_PRICE_UPDATED, self._handle_price_updated)
        event_manager.unsubscribe(EventType.PURCHASE_ITEM_RECEIVED, self._handle_item_received)
        
    def setup_connections(self):
        """Configure les connexions des signaux et slots"""
        # Connexion des boutons d'action
        if hasattr(self, 'add_button'):
            self.add_button.clicked.connect(self.on_add_button_clicked)
        if hasattr(self, 'edit_button'):
            self.edit_button.clicked.connect(self.on_edit_button_clicked)
        if hasattr(self, 'delete_button'):
            self.delete_button.clicked.connect(self.on_delete_button_clicked)
        if hasattr(self, 'scan_button'):
            self.scan_button.clicked.connect(self.on_scan_button_clicked)
        if hasattr(self, 'movement_button'):
            self.movement_button.clicked.connect(self.on_movement_button_clicked)
        if hasattr(self, 'category_button'):
            self.category_button.clicked.connect(self.on_category_button_clicked)
        if hasattr(self, 'export_button'):
            self.export_button.clicked.connect(self.on_export_button_clicked)
        if hasattr(self, 'refresh_button'):
            self.refresh_button.clicked.connect(self._load_data_wrapper)
            
    def on_delete_button_clicked(self):
        """Gère le clic sur le bouton Supprimer Article"""
        # Vérifier qu'un article est sélectionné
        if not hasattr(self, 'table_view') or not self.table_view.selectionModel().hasSelection():
            QMessageBox.warning(self, "Aucune sélection", "Veuillez sélectionner un article à supprimer.")
            return
            
        # Récupérer l'article sélectionné
        selected_row = self.table_view.selectionModel().currentIndex().row()
        if selected_row < 0:
            return
            
        # Récupérer l'ID de l'article
        item_id = self.proxy_model.data(self.proxy_model.index(selected_row, 0), Qt.ItemDataRole.DisplayRole)
        
        # Demander confirmation
        reply = QMessageBox.question(
            self, 
            "Confirmer la suppression",
            f"Êtes-vous sûr de vouloir supprimer cet article ?\nCette action est irréversible.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # Supprimer l'article
            self._delete_item(item_id)
    
    def _delete_item(self, item_id):
        """Supprime un article de l'inventaire"""
        try:
            # Créer une boucle d'événements asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Créer le service d'inventaire
            from app.core.services.inventory_service import InventoryService
            from app.utils.database import get_db
            db = next(get_db())
            service = InventoryService(db)
            
            # Supprimer l'article
            success = loop.run_until_complete(service.delete(item_id))
            
            # Fermer la boucle
            loop.close()
            
            if success:
                QMessageBox.information(self, "Suppression réussie", "L'article a été supprimé avec succès.")
                # Actualiser les données
                self._load_data_wrapper()
            else:
                QMessageBox.warning(self, "Échec de la suppression", "Impossible de supprimer l'article.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue lors de la suppression : {str(e)}")
            import traceback
            traceback.print_exc()
            
    def on_add_button_clicked(self):
        """Gère le clic sur le bouton Nouvel Article"""
        try:
            dialog = ItemDialog(parent=self)
            if dialog.exec():
                # Actualiser les données après l'ajout
                self._load_data_wrapper()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout d'un article : {str(e)}")
            import traceback
            traceback.print_exc()
    
    def on_edit_button_clicked(self):
        """Gère le clic sur le bouton Modifier Article"""
        # Vérifier qu'un article est sélectionné
        if not hasattr(self, 'table_view') or not self.table_view.selectionModel().hasSelection():
            return
            
        # Récupérer l'article sélectionné
        selected_row = self.table_view.selectionModel().currentIndex().row()
        if selected_row < 0:
            return
            
        # Récupérer l'ID de l'article
        item_id = self.proxy_model.data(self.proxy_model.index(selected_row, 0), Qt.ItemDataRole.DisplayRole)
        
        try:
            dialog = ItemDialog(parent=self, item_id=item_id)
            if dialog.exec():
                # Actualiser les données après la modification
                self._load_data_wrapper()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la modification de l'article : {str(e)}")
            import traceback
            traceback.print_exc()
    
    def on_scan_button_clicked(self):
        """Gère le clic sur le bouton Scanner code-barres"""
        # Fonctionnalité de scan à implémenter (BarcodeScannerDialog manquant)
        QMessageBox.information(self, "Scanner code-barres", "Fonctionnalité de scan à implémenter.")
    
    def on_movement_button_clicked(self):
        """Gère le clic sur le bouton Enregistrer Mouvement"""
        # Vérifier qu'un article est sélectionné
        if not hasattr(self, 'table_view') or not self.table_view.selectionModel().hasSelection():
            return
            
        # Récupérer l'article sélectionné
        selected_row = self.table_view.selectionModel().currentIndex().row()
        if selected_row < 0:
            return
            
        # Récupérer l'ID de l'article
        item_id = self.proxy_model.data(self.proxy_model.index(selected_row, 0), Qt.ItemDataRole.DisplayRole)
        
        try:
            dialog = MovementDialog(parent=self, item_id=item_id)
            if dialog.exec():
                # Actualiser les données après l'enregistrement du mouvement
                self._load_data_wrapper()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement du mouvement : {str(e)}")
            import traceback
            traceback.print_exc()
    
    def on_category_button_clicked(self):
        """Gère le clic sur le bouton Gérer Catégories"""
        try:
            from .category_view import CategoryDialog
            dialog = CategoryDialog(parent=self)
            if dialog.exec():
                # Actualiser les données après la modification des catégories
                self._load_data_wrapper()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la gestion des catégories : {str(e)}")
            import traceback
            traceback.print_exc()
    
    def on_export_button_clicked(self):
        """Gère le clic sur le bouton Exporter"""
        try:
            from PyQt6.QtWidgets import (
                QFileDialog, QDialog, QVBoxLayout, QDialogButtonBox, QCheckBox,
                QLabel, QPushButton, QHBoxLayout, QScrollArea, QWidget
            )
            import pandas as pd
            import csv
            from app.utils.export_utils import export_to_excel

            # Demander le format d'export
            format_dialog = QMessageBox()
            format_dialog.setWindowTitle("Format d'export")
            format_dialog.setText("Choisissez le format d'export :")
            excel_button = format_dialog.addButton("Excel", QMessageBox.ButtonRole.ActionRole)
            csv_button = format_dialog.addButton("CSV", QMessageBox.ButtonRole.ActionRole)
            cancel_button = format_dialog.addButton("Annuler", QMessageBox.ButtonRole.RejectRole)

            format_dialog.exec()

            clicked_button = format_dialog.clickedButton()
            if clicked_button == cancel_button:
                return

            # Sélection des colonnes
            model = self.table_view.model()
            all_headers = [model.headerData(i, Qt.Orientation.Horizontal) or f"Colonne {i}" for i in range(model.columnCount())]

            col_dialog = QDialog(self)
            col_dialog.setWindowTitle("Colonnes à exporter")
            main_layout = QVBoxLayout(col_dialog)
            main_layout.addWidget(QLabel("Sélectionnez les colonnes à inclure :"))

            scroll = QScrollArea()
            scroll.setWidgetResizable(True)
            container = QWidget()
            vbox = QVBoxLayout(container)
            checkboxes = []
            for idx, header in enumerate(all_headers):
                cb = QCheckBox(str(header))
                cb.setChecked(True)
                cb.setProperty("col_index", idx)
                vbox.addWidget(cb)
                checkboxes.append(cb)
            scroll.setWidget(container)
            main_layout.addWidget(scroll)

            tools_layout = QHBoxLayout()
            btn_all = QPushButton("Tout cocher")
            btn_none = QPushButton("Tout décocher")
            btn_all.clicked.connect(lambda: [cb.setChecked(True) for cb in checkboxes])
            btn_none.clicked.connect(lambda: [cb.setChecked(False) for cb in checkboxes])
            tools_layout.addWidget(btn_all)
            tools_layout.addWidget(btn_none)
            main_layout.addLayout(tools_layout)

            buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
            buttons.accepted.connect(col_dialog.accept)
            buttons.rejected.connect(col_dialog.reject)
            main_layout.addWidget(buttons)

            while True:
                if not col_dialog.exec():
                    return
                selected_cols = [cb.property("col_index") for cb in checkboxes if cb.isChecked()]
                if selected_cols:
                    break
                QMessageBox.warning(self, "Validation", "Sélectionnez au moins une colonne.")

            # Générer un nom de fichier par défaut avec timestamp
            from datetime import datetime
            import os
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            if clicked_button == excel_button:
                default_name = f"Inventaire_{timestamp}.xlsx"
            else:
                default_name = f"Inventaire_{timestamp}.csv"

            # Demander le chemin du fichier
            file_filter = "Fichiers Excel (*.xlsx)" if clicked_button == excel_button else "Fichiers CSV (*.csv)"
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Exporter l'inventaire",
                os.path.expanduser(f"~/Documents/{default_name}"),
                file_filter
            )

            if not file_path:
                return

            # Forcer l'extension si manquante
            if clicked_button == excel_button and not file_path.lower().endswith(".xlsx"):
                file_path += ".xlsx"
            if clicked_button == csv_button and not file_path.lower().endswith(".csv"):
                file_path += ".csv"

            # Préparer les données (respecte les filtres du proxy)
            headers = [all_headers[i] for i in selected_cols]
            rows = []
            for r in range(model.rowCount()):
                row = [model.data(model.index(r, c), Qt.ItemDataRole.DisplayRole) for c in selected_cols]
                rows.append(row)

            # Exporter les données
            if clicked_button == excel_button:
                df = pd.DataFrame(rows, columns=headers)
                success = export_to_excel({"Inventaire": df}, file_path, self)
                if success:
                    QMessageBox.information(self, "Export réussi", f"L'inventaire a été exporté avec succès vers {file_path}")
            elif clicked_button == csv_button:
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(headers)
                    writer.writerows(rows)
                QMessageBox.information(self, "Export réussi", f"L'inventaire a été exporté avec succès vers {file_path}")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export : {str(e)}")
            import traceback
            traceback.print_exc()
            
    def on_selection_changed(self):
        """Gère le changement de sélection dans la table"""
        has_selection = self.table_view.selectionModel().hasSelection()
        
        # Activer/désactiver les boutons qui nécessitent une sélection
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
        self.movement_button.setEnabled(has_selection)

    def closeEvent(self, event):
        """Gère l'événement de fermeture de la vue"""
        # Se désabonner des événements
        self._unsubscribe_from_events()

        # Accepter l'événement de fermeture
        event.accept()

    def refresh_inventory_data(self):
        """Actualise les données de l'inventaire lorsque le stock est mis à jour"""
        import logging
        logger = logging.getLogger(__name__)
        logger.info("Signal stock_updated reçu - Actualisation des données de l'inventaire")

        # Forcer l'actualisation immédiate des données
        self._load_data_wrapper()

        # Afficher un message pour confirmer l'actualisation
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(self, "Actualisation du stock", "Le stock a été mis à jour suite à l'ajout de pièces à une réparation.")

    def _init_data(self):
        """Initialize data loading"""
        # Utiliser notre wrapper pour exécuter load_data de manière asynchrone
        self._load_data_wrapper()

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        import asyncio
        import logging
        logger = logging.getLogger(__name__)
        logger.info("Début de _load_data_wrapper dans la vue d'inventaire")

        # Désactiver le bouton d'actualisation pendant le chargement
        if hasattr(self, 'refresh_button'):
            self.refresh_button.setEnabled(False)
            self.refresh_button.setText("Actualisation...")

        # Vérifier si nous sommes dans le thread principal
        from PyQt6.QtCore import QThread
        current_thread = QThread.currentThread()
        main_thread = QThread.currentThread()
        logger.info(f"Thread actuel: {current_thread}, Thread principal: {main_thread}")

        # Toujours créer une nouvelle boucle d'événements
        try:
            # Créer une nouvelle boucle d'événements
            logger.info("Création d'une nouvelle boucle d'événements")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Exécuter load_data de manière synchrone
            loop.run_until_complete(self.load_data())

            # Fermer la boucle après utilisation
            loop.close()

            logger.info("Données de l'inventaire chargées avec succès")

            # Afficher un message de statut temporaire si disponible
            if hasattr(self.parent(), 'statusBar'):
                self.parent().statusBar().showMessage("Inventaire actualisé avec succès", 3000)
        except Exception as e:
            logger.error(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()

            # Afficher un message d'erreur
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'actualisation de l'inventaire: {str(e)}")
        finally:
            # Réactiver le bouton d'actualisation
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(True)
                self.refresh_button.setText("Actualiser")

    def setup_ui(self):
        """Configure l'interface utilisateur de la vue inventaire (style clair modernisé)"""
        main_layout = QVBoxLayout(self)

        # En-tête modernisé avec fond blanc et ombre légère
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: #fff;
                border-bottom: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 12px 18px 12px 18px;
                margin-bottom: 8px;
            }
        """)
        header_layout = QHBoxLayout(header_frame)
        header_layout.setSpacing(18)
        header_layout.setContentsMargins(0, 0, 0, 0)

        # (Titre "Inventaire" supprimé, header et boutons conservés)

        # Espacement flexible avant les boutons
        header_layout.addStretch(1)

        # Boutons d'action modernisés
        button_style = (
            "QPushButton { background: #f5f7fa; color: #1976D2; border: 1px solid #e0e0e0; border-radius: 6px; padding: 8px 18px; font-weight: 600; margin-top: 65px; } "
            "QPushButton:hover { background: #e3f0fc; color: #1565c0; border: 1px solid #90caf9; } "
            "QPushButton:pressed { background: #bbdefb; color: #0d47a1; } "
            "QPushButton:disabled { background: #f5f5f5; color: #bdbdbd; border: 1px solid #eeeeee; } "
        )


        self.add_button = QPushButton("Nouvel Article")
        self.add_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.add_button.setStyleSheet(button_style)
        self.add_button.setToolTip("Ajouter un nouvel article à l'inventaire (Ctrl+N)")

        # Bouton scanner code-barres
        self.scan_button = QPushButton("Scanner code-barres")
        self.scan_button.setIcon(QIcon("app/ui/resources/icons/barcode.svg"))
        self.scan_button.setStyleSheet(button_style)
        self.scan_button.setToolTip("Scanner un code-barres pour ajouter ou éditer un article rapidement")

        self.movement_button = QPushButton("Enregistrer Mouvement")
        self.movement_button.setIcon(QIcon("app/ui/resources/icons/movement.svg"))
        self.movement_button.setEnabled(False)
        self.movement_button.setStyleSheet(button_style)
        self.movement_button.setToolTip("Enregistrer un mouvement de stock (Ctrl+M)")

        self.edit_button = QPushButton("Modifier Article")
        self.edit_button.setIcon(QIcon("app/ui/resources/icons/edit.svg"))
        self.edit_button.setEnabled(False)
        self.edit_button.setStyleSheet(button_style)
        self.edit_button.setToolTip("Modifier l'article sélectionné (Ctrl+E)")
        
        self.delete_button = QPushButton("Supprimer Article")
        self.delete_button.setIcon(QIcon("app/ui/resources/icons/delete.svg"))
        self.delete_button.setEnabled(False)
        self.delete_button.setStyleSheet(button_style)
        self.delete_button.setToolTip("Supprimer l'article sélectionné")

        self.category_button = QPushButton("Gérer Catégories")
        self.category_button.setIcon(QIcon("app/ui/resources/icons/category.svg"))
        self.category_button.setStyleSheet(button_style)
        self.category_button.setToolTip("Gérer les catégories d'articles")

        self.export_button = QPushButton("Exporter")
        self.export_button.setIcon(QIcon("app/ui/resources/icons/export.svg"))
        self.export_button.setStyleSheet(button_style)
        self.export_button.setToolTip("Exporter l'inventaire (Excel, CSV...)")

        self.refresh_button = QPushButton("Actualiser")
        self.refresh_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        self.refresh_button.setStyleSheet(button_style)
        self.refresh_button.setToolTip("Actualiser la liste de l'inventaire (F5)")

        # Ajout des boutons avec espace entre chaque dans l'ordre demandé
        for btn in [self.scan_button, self.add_button, self.edit_button, self.delete_button, self.category_button, self.export_button, self.refresh_button, self.movement_button]:
            header_layout.addWidget(btn)

        main_layout.addWidget(header_frame)

        # Barre de recherche et filtres modernisée
        search_layout = QHBoxLayout()
        search_layout.setSpacing(14)
        search_layout.setContentsMargins(0, 0, 0, 0)

        # Barre de recherche avec icône
        self.search_bar = SearchBar("Rechercher un article...")
        self.search_bar.setFixedHeight(36)
        self.search_bar.setStyleSheet("""
            QLineEdit {
                background: #f5f7fa;
                border: 1.5px solid #e0e0e0;
                border-radius: 18px;
                padding-left: 36px;
                font-size: 15px;
                color: #222;
            }
            QLineEdit:focus {
                border: 1.5px solid #1976D2;
                background: #fff;
            }
        """)
        # Ajout d'une icône de recherche (si SearchBar le permet)
        if hasattr(self.search_bar, 'set_icon'):
            self.search_bar.set_icon(QIcon("app/ui/resources/icons/search.svg"))

        # Filtres déroulants modernisés
        combo_style = (
            "QComboBox { background: #f5f7fa; border: 1.5px solid #e0e0e0; border-radius: 16px; padding: 6px 24px 6px 14px; font-size: 15px; color: #1976D2; min-width: 120px; } "
            "QComboBox:focus { border: 1.5px solid #1976D2; background: #fff; } "
            "QComboBox::drop-down { border: none; } "
            "QComboBox QAbstractItemView { background: #fff; border-radius: 8px; } "
        )
        self.category_filter = FilterComboBox("Catégorie")
        self.category_filter.setStyleSheet(combo_style)
        self.category_filter.setFixedHeight(36)
        self.status_filter = FilterComboBox("Statut")
        self.status_filter.setStyleSheet(combo_style)
        self.status_filter.setFixedHeight(36)
        self.location_filter = FilterComboBox("Emplacement")
        self.location_filter.setStyleSheet(combo_style)
        self.location_filter.setFixedHeight(36)
        
        # Filtre pour l'état (condition)
        self.condition_filter = FilterComboBox("État")
        self.condition_filter.setStyleSheet(combo_style)
        self.condition_filter.setFixedHeight(36)

        search_layout.addWidget(self.search_bar, stretch=2)
        search_layout.addWidget(self.category_filter, stretch=1)
        search_layout.addWidget(self.status_filter, stretch=1)
        search_layout.addWidget(self.location_filter, stretch=1)
        search_layout.addWidget(self.condition_filter, stretch=1)

        main_layout.addLayout(search_layout)

        # Nouvelle organisation : zone supérieure (widgets d'alerte et valorisation côte à côte), zone inférieure (tableau)
        widgets_layout = QHBoxLayout()
        widgets_layout.setSpacing(24)

        # Style simple et plat pour les widgets d'information
        widget_simple_style = """
            QFrame {
                background: transparent;
                border: none;
                border-radius: 0;
                padding: 0;
                margin: 0;
            }
        """
        # Widget d'alertes de stock (aligné, sans marge gauche)
        self.alert_widget = StockAlertWidget()
        # Supprimer le style simple ici pour conserver son style propre aligné au tableau
        self.alert_widget.setMinimumWidth(0)
        self.alert_widget.setMaximumWidth(16777215)  # Pas de limite
        # Hauteur réduite pour mieux s'intégrer visuellement
        self.alert_widget.setFixedHeight(220)
        self.alert_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        # Zéro marges sur le conteneur pour éviter l'espace à gauche
        widgets_layout.setContentsMargins(0, 0, 0, 0)
        # Prendre 50% de la largeur (même stretch que le widget de valorisation)
        widgets_layout.addWidget(self.alert_widget, stretch=1)

        # Widget de valorisation des stocks (plus compact)
        self.valuation_widget = ValuationWidget()
        self.valuation_widget.product_selected.connect(self.show_price_history_dialog)
        # Garder un style sobre
        self.valuation_widget.setMinimumWidth(0)
        self.valuation_widget.setMaximumWidth(16777215)
        self.valuation_widget.setFixedHeight(220)
        self.valuation_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        # Même stretch que l'alerte pour aligner le centre de page
        widgets_layout.addWidget(self.valuation_widget, stretch=1)

        main_layout.addLayout(widgets_layout)

        # Tableau d'inventaire en bas (30% de la hauteur)
        self.table_view = QTableView()
        self.table_view.setObjectName("inventoryTable")
        self.table_model = InventoryTableModel()
        self.proxy_model = CustomFilterProxyModel()
        self.proxy_model.setSourceModel(self.table_model)
        self.table_view.setModel(self.proxy_model)
        self.table_view.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        self.table_view.setSortingEnabled(True)
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table_view.horizontalHeader().setStretchLastSection(True)
        # Délégué pour afficher les badges colorés dans la colonne Statut (index 8)
        try:
            self.table_view.setItemDelegateForColumn(8, StatusBadgeDelegate(self.table_view))
        except Exception:
            pass
        self.table_view.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.table_view.customContextMenuRequested.connect(self.show_context_menu)
        self.table_view.setAlternatingRowColors(True)
        # Style modernisé du tableau
        self.table_view.setStyleSheet("""
            QTableView {
                background: #fff;
                border-radius: 12px;
                border: 1.5px solid #e0e0e0;
                font-size: 15px;
                color: #222;
                selection-background-color: #1976D2;
                selection-color: #fff;
                alternate-background-color: #f5f7fa;
                gridline-color: #e0e0e0;
                qproperty-alignment: 'AlignCenter';
            }
            QHeaderView::section {
                background: #f5f7fa;
                color: #1976D2;
                font-weight: bold;
                font-size: 15px;
                border: none;
                border-bottom: 2px solid #e0e0e0;
                border-radius: 8px 8px 0 0;
                padding: 8px 0;
                qproperty-alignment: 'AlignCenter';
                text-align: center;
            }
            QTableView::item {
                text-align: center;
            }
            QTableView::item:selected {
                background-color: #1976D2;
                color: #fff;
                font-weight: bold;
            }
            QTableView::item:hover {
                background-color: #e3f0fc;
            }
        """)
        # Limiter la hauteur du tableau à 30% de la fenêtre
        self.table_view.setMinimumHeight(180)
        self.table_view.setMaximumHeight(350)
        main_layout.addWidget(self.table_view)

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        self.loading_overlay.hide()

    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.add_button.clicked.connect(self.show_add_dialog)
        self.scan_button.clicked.connect(self.show_scan_dialog)
        self.movement_button.clicked.connect(self.show_movement_dialog)
        self.edit_button.clicked.connect(self.edit_selected_item)
        self.category_button.clicked.connect(self.show_category_view)
        self.export_button.clicked.connect(self.export_inventory)
        self.refresh_button.clicked.connect(self._load_data_wrapper)

        self.search_bar.textChanged.connect(self.filter_inventory)
        self.category_filter.currentTextChanged.connect(self.filter_inventory)
        self.status_filter.currentTextChanged.connect(self.filter_inventory)
        self.location_filter.currentTextChanged.connect(self.filter_inventory)
        self.condition_filter.currentIndexChanged.connect(self.filter_inventory)

        self.table_view.doubleClicked.connect(self.show_edit_dialog)
        self.table_view.selectionModel().selectionChanged.connect(self.on_selection_changed)

    def show_category_view(self):
        """Affiche la vue de gestion des catégories"""
        # Trouver la fenêtre principale
        main_window = self.window()
        if hasattr(main_window, 'central_widget') and hasattr(main_window, 'category'):
            main_window.central_widget.setCurrentWidget(main_window.category)

    async def load_data(self):
        """Charge les données de l'inventaire"""
        self.loading_overlay.show()
        try:
            # Charger les données du tableau
            await self.table_model.load_data()

            # Charger les filtres
            await self.load_filters()

            # Charger les alertes
            try:
                await self.alert_widget.update_alerts()
            except Exception:
                pass

            # Charger les données de valorisation
            self.valuation_widget.load_data()

        except Exception:
            pass
        finally:
            self.loading_overlay.hide()

    async def load_filters(self):
        """Charge les options des filtres"""
        try:
            # Charger les catégories
            from app.core.services.item_category_service import ItemCategoryService
            category_service = ItemCategoryService()

            self.category_filter.clear()
            self.category_filter.addItem("Toutes les catégories", "")

            # Récupérer les catégories depuis le service
            categories = await category_service.get_all_categories()

            for category in categories:
                self.category_filter.addItem(category.name, category.id)

            # Charger les statuts
            from app.core.models.inventory import ItemStatus
            self.status_filter.clear()
            self.status_filter.addItem("Tous les statuts", "")
            status_map = {
                'available': 'Disponible',
                'low_stock': 'Stock bas',
                'out_of_stock': 'Rupture',
                'discontinued': 'Arrêté',
            }
            for status in ItemStatus:
                label = status_map.get(status.value, status.value)
                self.status_filter.addItem(label, status.value)

            # Charger les emplacements
            self.location_filter.clear()
            self.location_filter.addItem("Tous les emplacements", "")

            # Récupérer les emplacements uniques depuis le service
            locations = await self.table_model.service.get_unique_locations()

            for location in locations:
                if location:  # Ne pas ajouter les emplacements vides
                    self.location_filter.addItem(location, location)

            # Charger les conditions (états)
            from app.core.models.inventory import ItemCondition
            self.condition_filter.clear()
            self.condition_filter.addItem("Tous les états", "")
            # Map enum values ('new'/'used') to French labels
            condition_map = {
                'new': 'Neuf',
                'used': 'Occasion',
            }
            for condition in ItemCondition:
                value = getattr(condition, "value", str(condition))
                label = condition_map.get(value, value)
                self.condition_filter.addItem(label, value)

            print("Filtres chargés avec succès")
        except Exception as e:
            print(f"Erreur lors du chargement des filtres: {e}")
            import traceback
            traceback.print_exc()

    def filter_inventory(self):
        """Applique les filtres sur le tableau"""
        # Récupérer les valeurs des filtres
        search_text = self.search_bar.text().lower()

        # Récupérer les valeurs des filtres déroulants
        category_index = self.category_filter.currentIndex()
        category = self.category_filter.currentText() if category_index > 0 else ""

        status_index = self.status_filter.currentIndex()
        status = self.status_filter.itemData(status_index) if status_index > 0 else ""

        location_index = self.location_filter.currentIndex()
        location = self.location_filter.currentText() if location_index > 0 else ""

        condition_index = self.condition_filter.currentIndex()
        condition = self.condition_filter.itemData(condition_index) if condition_index > 0 else ""

        # Appliquer les filtres
        self.proxy_model.set_filter_text(search_text)
        self.proxy_model.set_filter_category(category)
        self.proxy_model.set_filter_status(status)
        self.proxy_model.set_filter_location(location)
        self.proxy_model.set_filter_condition(condition)

        # Afficher le nombre de résultats filtrés
        total_rows = self.table_model.rowCount()
        filtered_rows = self.proxy_model.rowCount()
        print(f"Filtrage: {filtered_rows} résultats sur {total_rows} articles")

    def show_add_dialog(self):
        """Affiche la boîte de dialogue d'ajout d'article"""
        dialog = ItemDialog(self)
        if dialog.exec():
            # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
            QTimer.singleShot(0, self._load_data_wrapper)

    def show_edit_dialog(self, index):
        """Affiche la boîte de dialogue d'édition d'article"""
        item_id = self.table_model.get_item_id(index.row())
        dialog = ItemDialog(self, item_id=item_id)
        if dialog.exec():
            # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
            QTimer.singleShot(0, self._load_data_wrapper)

    def show_movement_dialog(self):
        """Affiche la boîte de dialogue d'enregistrement de mouvement"""
        dialog = MovementDialog(self)
        if dialog.exec():
            # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
            QTimer.singleShot(0, self._load_data_wrapper)

    def show_scan_dialog(self):
        """Affiche la boîte de dialogue de scan de code-barres pour ajout/édition rapide"""
        # TODO: Implémenter la logique de scan (ouvrir une boîte de dialogue dédiée ou utiliser un widget de scan)
        QMessageBox.information(self, "Scanner code-barres", "Fonctionnalité de scan à implémenter.")

    def export_inventory(self):
        """Exporte les données de l'inventaire (CSV/Excel) avec sélection de colonnes"""
        try:
            from PyQt6.QtWidgets import (
                QFileDialog, QDialog, QVBoxLayout, QDialogButtonBox, QCheckBox,
                QLabel, QPushButton, QHBoxLayout, QScrollArea, QWidget
            )
            import pandas as pd
            import csv
            from app.utils.export_utils import export_to_excel

            # Vérifications de base
            if not hasattr(self, 'table_view') or self.table_view.model() is None:
                QMessageBox.warning(self, "Export", "La table d'inventaire n'est pas disponible.")
                return

            # Choix du format
            format_dialog = QMessageBox(self)
            format_dialog.setWindowTitle("Format d'export")
            format_dialog.setText("Choisissez le format d'export :")
            excel_button = format_dialog.addButton("Excel", QMessageBox.ButtonRole.ActionRole)
            csv_button = format_dialog.addButton("CSV", QMessageBox.ButtonRole.ActionRole)
            cancel_button = format_dialog.addButton("Annuler", QMessageBox.ButtonRole.RejectRole)
            format_dialog.exec()

            clicked_button = format_dialog.clickedButton()
            if clicked_button == cancel_button:
                return

            # Sélection des colonnes
            model = self.table_view.model()  # proxy -> respecte filtres/tri
            all_headers = [model.headerData(i, Qt.Orientation.Horizontal) or f"Colonne {i}" for i in range(model.columnCount())]

            col_dialog = QDialog(self)
            col_dialog.setWindowTitle("Colonnes à exporter")
            main_layout = QVBoxLayout(col_dialog)
            main_layout.addWidget(QLabel("Sélectionnez les colonnes à inclure :"))

            scroll = QScrollArea()
            scroll.setWidgetResizable(True)
            container = QWidget()
            vbox = QVBoxLayout(container)
            checkboxes = []
            for idx, header in enumerate(all_headers):
                cb = QCheckBox(str(header))
                cb.setChecked(True)
                cb.setProperty("col_index", idx)
                vbox.addWidget(cb)
                checkboxes.append(cb)
            scroll.setWidget(container)
            main_layout.addWidget(scroll)

            tools_layout = QHBoxLayout()
            btn_all = QPushButton("Tout cocher")
            btn_none = QPushButton("Tout décocher")
            btn_all.clicked.connect(lambda: [cb.setChecked(True) for cb in checkboxes])
            btn_none.clicked.connect(lambda: [cb.setChecked(False) for cb in checkboxes])
            tools_layout.addWidget(btn_all)
            tools_layout.addWidget(btn_none)
            main_layout.addLayout(tools_layout)

            buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
            buttons.accepted.connect(col_dialog.accept)
            buttons.rejected.connect(col_dialog.reject)
            main_layout.addWidget(buttons)

            while True:
                if not col_dialog.exec():
                    return
                selected_cols = [cb.property("col_index") for cb in checkboxes if cb.isChecked()]
                if selected_cols:
                    break
                QMessageBox.warning(self, "Validation", "Sélectionnez au moins une colonne.")

            # Générer un nom de fichier par défaut avec timestamp
            from datetime import datetime
            import os
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            if clicked_button == excel_button:
                default_name = f"Inventaire_{timestamp}.xlsx"
            else:
                default_name = f"Inventaire_{timestamp}.csv"

            # Choix du fichier
            file_filter = "Fichiers Excel (*.xlsx)" if clicked_button == excel_button else "Fichiers CSV (*.csv)"
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Exporter l'inventaire",
                os.path.expanduser(f"~/Documents/{default_name}"),
                file_filter
            )
            if not file_path:
                return

            # Extension
            if clicked_button == excel_button and not file_path.lower().endswith(".xlsx"):
                file_path += ".xlsx"
            if clicked_button == csv_button and not file_path.lower().endswith(".csv"):
                file_path += ".csv"

            # Préparer les données
            headers = [all_headers[i] for i in selected_cols]
            rows = []
            for r in range(model.rowCount()):
                row = [model.data(model.index(r, c), Qt.ItemDataRole.DisplayRole) for c in selected_cols]
                rows.append(row)

            # Export
            if clicked_button == excel_button:
                df = pd.DataFrame(rows, columns=headers)
                success = export_to_excel({"Inventaire": df}, file_path, self)
                if success:
                    QMessageBox.information(self, "Export réussi", f"Inventaire exporté vers {file_path}")
            else:  # CSV
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(headers)
                    writer.writerows(rows)
                QMessageBox.information(self, "Export réussi", f"Inventaire exporté vers {file_path}")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export : {str(e)}")
            import traceback
            traceback.print_exc()

    def on_selection_changed(self, selected, deselected):
        """Gère le changement de sélection dans le tableau"""
        has_selection = len(self.table_view.selectionModel().selectedRows()) > 0

        # Activer/désactiver les boutons en fonction de la sélection
        self.movement_button.setEnabled(has_selection)
        self.edit_button.setEnabled(has_selection)

        if has_selection:
            # Récupérer l'article sélectionné
            selected_row = self.table_view.selectionModel().selectedRows()[0]
            source_index = self.proxy_model.mapToSource(selected_row)
            self.selected_item_id = self.table_model.get_item_id(source_index.row())

            # Mettre en évidence l'article sélectionné
            self.table_view.scrollTo(selected_row)

            # Afficher un message dans la barre d'état (si disponible)
            if hasattr(self.parent(), 'statusBar'):
                item_name = self.table_model.data(source_index.sibling(source_index.row(), 1))
                self.parent().statusBar().showMessage(f"Article sélectionné: {item_name}")
        else:
            # Réinitialiser l'ID de l'article sélectionné
            self.selected_item_id = None

            # Effacer le message de la barre d'état
            if hasattr(self.parent(), 'statusBar'):
                self.parent().statusBar().clearMessage()

    def edit_selected_item(self):
        """Édite l'article sélectionné"""
        if hasattr(self, 'selected_item_id') and self.selected_item_id:
            dialog = ItemDialog(self, item_id=self.selected_item_id)
            if dialog.exec():
                # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
                QTimer.singleShot(0, self._load_data_wrapper)

    def show_context_menu(self, position):
        """Affiche le menu contextuel pour l'article sélectionné"""
        # Vérifier si un article est sélectionné
        if not hasattr(self, 'selected_item_id') or not self.selected_item_id:
            return

        # Créer le menu contextuel
        context_menu = QMenu(self)

        # Ajouter les actions
        edit_action = QAction(QIcon("app/ui/resources/icons/edit.svg"), "Modifier", self)
        edit_action.triggered.connect(self.edit_selected_item)
        context_menu.addAction(edit_action)

        movement_action = QAction(QIcon("app/ui/resources/icons/movement.svg"), "Enregistrer un mouvement", self)
        movement_action.triggered.connect(self.show_movement_dialog)
        context_menu.addAction(movement_action)

        # Ajouter un séparateur
        context_menu.addSeparator()

        # Récupérer l'article sélectionné pour afficher des informations
        selected_row = self.table_view.selectionModel().selectedRows()[0]
        source_index = self.proxy_model.mapToSource(selected_row)
        item_name = self.table_model.data(source_index.sibling(source_index.row(), 1))
        item_quantity = self.table_model.data(source_index.sibling(source_index.row(), 3))

        # Ajouter des informations sur l'article
        info_label = QAction(f"Article: {item_name} (Quantité: {item_quantity})", self)
        info_label.setEnabled(False)
        context_menu.addAction(info_label)

        # Afficher le menu à la position du clic
        context_menu.exec(self.table_view.mapToGlobal(position))

    def show_price_history_dialog(self, product_id):
        """Affiche la boîte de dialogue d'historique des prix d'un produit"""
        try:
            dialog = PriceHistoryDialog(self, product_id=product_id)
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Erreur lors de l'affichage de l'historique des prix: {str(e)}"
            )

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.load_data())
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    def setup_table(self):
        """Configure le tableau d'inventaire avec les badges de statut"""
        # Configuration du tableau
        self.table_view = QTableView()
        self.table_view.setAlternatingRowColors(True)
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table_view.setSortingEnabled(True)
        self.table_view.setWordWrap(False)
        self.table_view.setShowGrid(True)
        self.table_view.setGridStyle(Qt.PenStyle.SolidLine)
        
        # Style du tableau
        self.table_view.setStyleSheet("""
            QTableView {
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                gridline-color: #e0e0e0;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                selection-background-color: #e3f2fd;
                selection-color: #1976d2;
            }
            QTableView::item {
                padding: 8px;
                border: none;
            }
            QTableView::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QHeaderView::section {
                background-color: #f5f7fa;
                color: #1976d2;
                padding: 12px 8px;
                border: none;
                border-bottom: 2px solid #e0e0e0;
                font-weight: 600;
                font-size: 13px;
            }
            QHeaderView::section:hover {
                background-color: #e3f0fc;
            }
        """)
        
        # Configuration des en-têtes
        header = self.table_view.horizontalHeader()
        header.setStretchLastSection(False)
        header.setDefaultAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Définir les largeurs de colonnes
        column_widths = {
            0: 100,  # SKU
            1: 200,  # Nom
            2: 120,  # Catégorie
            3: 80,   # Quantité
            4: 120,  # Emplacement
            5: 100,  # Prix d'achat
            6: 100,  # Prix de vente
            7: 120,  # Marge bénéficiaire
            8: 120,  # Statut
        }
        
        for col, width in column_widths.items():
            self.table_view.setColumnWidth(col, width)
        
        # Rendre certaines colonnes redimensionnables
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Nom
        
        # Activer le tri sur toutes les colonnes
        header.setSortIndicatorShown(True)
        
        # Appliquer le délégué personnalisé pour les badges de statut
        self.table_view.setItemDelegateForColumn(8, StatusBadgeDelegate(self.table_view))  # Colonne Statut
        
        # Connecter les signaux
        self.table_view.selectionModel().selectionChanged.connect(self._on_selection_changed)
        self.table_view.doubleClicked.connect(self._on_item_double_clicked)
        
        # Ajouter le tableau au layout
        self.main_layout.addWidget(self.table_view)

